"""
Менеджер кэширования для оптимизации производительности бота
"""
import time
import asyncio
from typing import Dict, Any, Optional, Callable
from functools import wraps
from aiogram.types import InlineKeyboardMarkup

# Глобальные кэши
_keyboard_cache: Dict[str, InlineKeyboardMarkup] = {}
_data_cache: Dict[str, Any] = {}
_cache_timestamps: Dict[str, float] = {}
_cache_locks: Dict[str, asyncio.Lock] = {}

# Настройки кэширования
DEFAULT_TTL = 300  # 5 минут
KEYBOARD_TTL = 1800  # 30 минут для клавиатур
STATIC_DATA_TTL = 3600  # 1 час для статических данных


class CacheManager:
    """Менеджер кэширования для оптимизации производительности"""
    
    @staticmethod
    def get_cache_key(*args, **kwargs) -> str:
        """Генерация ключа кэша из аргументов"""
        key_parts = []
        for arg in args:
            if isinstance(arg, (str, int, float, bool)):
                key_parts.append(str(arg))
            else:
                key_parts.append(str(type(arg).__name__))
        
        for k, v in sorted(kwargs.items()):
            if isinstance(v, (str, int, float, bool)):
                key_parts.append(f"{k}:{v}")
        
        return "_".join(key_parts)
    
    @staticmethod
    async def get_cached_keyboard(cache_key: str) -> Optional[InlineKeyboardMarkup]:
        """Получить клавиатуру из кэша"""
        if cache_key not in _keyboard_cache:
            return None
        
        # Проверяем TTL
        if cache_key in _cache_timestamps:
            if time.time() - _cache_timestamps[cache_key] > KEYBOARD_TTL:
                # Кэш устарел
                del _keyboard_cache[cache_key]
                del _cache_timestamps[cache_key]
                return None
        
        return _keyboard_cache[cache_key]
    
    @staticmethod
    async def cache_keyboard(cache_key: str, keyboard: InlineKeyboardMarkup):
        """Сохранить клавиатуру в кэш"""
        _keyboard_cache[cache_key] = keyboard
        _cache_timestamps[cache_key] = time.time()
    
    @staticmethod
    async def get_cached_data(cache_key: str, ttl: int = DEFAULT_TTL) -> Optional[Any]:
        """Получить данные из кэша"""
        if cache_key not in _data_cache:
            return None
        
        # Проверяем TTL
        if cache_key in _cache_timestamps:
            if time.time() - _cache_timestamps[cache_key] > ttl:
                # Кэш устарел
                del _data_cache[cache_key]
                del _cache_timestamps[cache_key]
                return None
        
        return _data_cache[cache_key]
    
    @staticmethod
    async def cache_data(cache_key: str, data: Any):
        """Сохранить данные в кэш"""
        _data_cache[cache_key] = data
        _cache_timestamps[cache_key] = time.time()
    
    @staticmethod
    async def invalidate_cache(pattern: str = None):
        """Инвалидировать кэш по паттерну или полностью"""
        if pattern is None:
            # Очищаем весь кэш
            _keyboard_cache.clear()
            _data_cache.clear()
            _cache_timestamps.clear()
        else:
            # Очищаем по паттерну
            keys_to_remove = []
            for key in _cache_timestamps.keys():
                if pattern in key:
                    keys_to_remove.append(key)
            
            for key in keys_to_remove:
                _keyboard_cache.pop(key, None)
                _data_cache.pop(key, None)
                _cache_timestamps.pop(key, None)
    
    @staticmethod
    def get_cache_stats() -> Dict[str, int]:
        """Получить статистику кэша"""
        return {
            "keyboards": len(_keyboard_cache),
            "data": len(_data_cache),
            "total_entries": len(_cache_timestamps)
        }


def cached_keyboard(ttl: int = KEYBOARD_TTL):
    """Декоратор для кэширования клавиатур"""
    def decorator(func: Callable):
        @wraps(func)
        async def wrapper(*args, **kwargs):
            # Генерируем ключ кэша
            cache_key = f"kb_{func.__name__}_{CacheManager.get_cache_key(*args, **kwargs)}"
            
            # Пытаемся получить из кэша
            cached_result = await CacheManager.get_cached_keyboard(cache_key)
            if cached_result is not None:
                return cached_result
            
            # Получаем блокировку для этого ключа
            if cache_key not in _cache_locks:
                _cache_locks[cache_key] = asyncio.Lock()
            
            async with _cache_locks[cache_key]:
                # Повторная проверка после получения блокировки
                cached_result = await CacheManager.get_cached_keyboard(cache_key)
                if cached_result is not None:
                    return cached_result
                
                # Выполняем функцию
                if asyncio.iscoroutinefunction(func):
                    result = await func(*args, **kwargs)
                else:
                    result = func(*args, **kwargs)
                
                # Кэшируем результат
                await CacheManager.cache_keyboard(cache_key, result)
                return result
        
        return wrapper
    return decorator


def cached_data(ttl: int = DEFAULT_TTL):
    """Декоратор для кэширования данных"""
    def decorator(func: Callable):
        @wraps(func)
        async def wrapper(*args, **kwargs):
            # Генерируем ключ кэша
            cache_key = f"data_{func.__name__}_{CacheManager.get_cache_key(*args, **kwargs)}"
            
            # Пытаемся получить из кэша
            cached_result = await CacheManager.get_cached_data(cache_key, ttl)
            if cached_result is not None:
                return cached_result
            
            # Получаем блокировку для этого ключа
            if cache_key not in _cache_locks:
                _cache_locks[cache_key] = asyncio.Lock()
            
            async with _cache_locks[cache_key]:
                # Повторная проверка после получения блокировки
                cached_result = await CacheManager.get_cached_data(cache_key, ttl)
                if cached_result is not None:
                    return cached_result
                
                # Выполняем функцию
                if asyncio.iscoroutinefunction(func):
                    result = await func(*args, **kwargs)
                else:
                    result = func(*args, **kwargs)
                
                # Кэшируем результат
                await CacheManager.cache_data(cache_key, result)
                return result
        
        return wrapper
    return decorator


# Функции для очистки кэша при обновлении данных
async def invalidate_user_cache(user_id: int):
    """Инвалидировать кэш для конкретного пользователя"""
    await CacheManager.invalidate_cache(f"user_{user_id}")


async def invalidate_subject_cache(subject_id: int):
    """Инвалидировать кэш для конкретного предмета"""
    await CacheManager.invalidate_cache(f"subject_{subject_id}")


async def invalidate_homework_cache(homework_id: int):
    """Инвалидировать кэш для конкретного ДЗ"""
    await CacheManager.invalidate_cache(f"homework_{homework_id}")


# Периодическая очистка устаревшего кэша
async def cleanup_expired_cache():
    """Очистка устаревшего кэша"""
    current_time = time.time()
    expired_keys = []
    
    for key, timestamp in _cache_timestamps.items():
        # Определяем TTL в зависимости от типа кэша
        if key.startswith("kb_"):
            ttl = KEYBOARD_TTL
        elif key.startswith("data_"):
            ttl = DEFAULT_TTL
        else:
            ttl = DEFAULT_TTL
        
        if current_time - timestamp > ttl:
            expired_keys.append(key)
    
    # Удаляем устаревшие записи
    for key in expired_keys:
        _keyboard_cache.pop(key, None)
        _data_cache.pop(key, None)
        _cache_timestamps.pop(key, None)
        _cache_locks.pop(key, None)
    
    if expired_keys:
        print(f"🧹 Очищено {len(expired_keys)} устаревших записей кэша")


# Запуск периодической очистки кэша
async def start_cache_cleanup_task():
    """Запуск задачи периодической очистки кэша"""
    while True:
        await asyncio.sleep(600)  # Каждые 10 минут
        await cleanup_expired_cache()
