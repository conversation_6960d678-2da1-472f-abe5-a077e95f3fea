"""
Скрипт для оптимизации базы данных - добавление индексов и настройка производительности
"""
import asyncio
import sys
import os

# Добавляем корневую папку проекта в путь
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from database import get_db_session, init_database
from sqlalchemy import text


async def create_performance_indexes():
    """Создание индексов для оптимизации производительности"""
    print("🚀 Начинаем оптимизацию базы данных...")
    
    # Инициализируем подключение к базе данных
    await init_database()
    
    async with get_db_session() as session:
        try:
            print("📊 Создание индексов для оптимизации...")
            
            # Индексы для таблицы users
            indexes_to_create = [
                # Основные индексы для быстрого поиска пользователей
                "CREATE INDEX IF NOT EXISTS idx_users_telegram_id ON users(telegram_id)",
                "CREATE INDEX IF NOT EXISTS idx_users_role ON users(role)",
                "CREATE INDEX IF NOT EXISTS idx_users_telegram_id_role ON users(telegram_id, role)",
                
                # Индексы для таблицы students
                "CREATE INDEX IF NOT EXISTS idx_students_user_id ON students(user_id)",
                "CREATE INDEX IF NOT EXISTS idx_students_group_id ON students(group_id)",
                "CREATE INDEX IF NOT EXISTS idx_students_user_group ON students(user_id, group_id)",
                
                # Индексы для таблицы curators
                "CREATE INDEX IF NOT EXISTS idx_curators_user_id ON curators(user_id)",
                "CREATE INDEX IF NOT EXISTS idx_curators_course_id ON curators(course_id)",
                "CREATE INDEX IF NOT EXISTS idx_curators_subject_id ON curators(subject_id)",
                "CREATE INDEX IF NOT EXISTS idx_curators_group_id ON curators(group_id)",
                
                # Индексы для таблицы teachers
                "CREATE INDEX IF NOT EXISTS idx_teachers_user_id ON teachers(user_id)",
                "CREATE INDEX IF NOT EXISTS idx_teachers_course_id ON teachers(course_id)",
                "CREATE INDEX IF NOT EXISTS idx_teachers_subject_id ON teachers(subject_id)",
                "CREATE INDEX IF NOT EXISTS idx_teachers_group_id ON teachers(group_id)",
                
                # Индексы для таблицы managers
                "CREATE INDEX IF NOT EXISTS idx_managers_user_id ON managers(user_id)",
                
                # Индексы для таблицы courses
                "CREATE INDEX IF NOT EXISTS idx_courses_name ON courses(name)",
                
                # Индексы для таблицы subjects
                "CREATE INDEX IF NOT EXISTS idx_subjects_name ON subjects(name)",
                
                # Индексы для таблицы groups
                "CREATE INDEX IF NOT EXISTS idx_groups_name ON groups(name)",
                "CREATE INDEX IF NOT EXISTS idx_groups_subject_id ON groups(subject_id)",
                
                # Индексы для таблицы microtopics
                "CREATE INDEX IF NOT EXISTS idx_microtopics_subject_id ON microtopics(subject_id)",
                "CREATE INDEX IF NOT EXISTS idx_microtopics_number ON microtopics(number)",
                "CREATE INDEX IF NOT EXISTS idx_microtopics_subject_number ON microtopics(subject_id, number)",
                
                # Индексы для таблицы lessons
                "CREATE INDEX IF NOT EXISTS idx_lessons_subject_id ON lessons(subject_id)",
                "CREATE INDEX IF NOT EXISTS idx_lessons_name ON lessons(name)",
                
                # Индексы для таблицы homework
                "CREATE INDEX IF NOT EXISTS idx_homework_course_id ON homework(course_id)",
                "CREATE INDEX IF NOT EXISTS idx_homework_subject_id ON homework(subject_id)",
                "CREATE INDEX IF NOT EXISTS idx_homework_lesson_id ON homework(lesson_id)",
                "CREATE INDEX IF NOT EXISTS idx_homework_created_by ON homework(created_by)",
                "CREATE INDEX IF NOT EXISTS idx_homework_created_at ON homework(created_at)",
                
                # Индексы для таблицы questions
                "CREATE INDEX IF NOT EXISTS idx_questions_homework_id ON questions(homework_id)",
                "CREATE INDEX IF NOT EXISTS idx_questions_order_num ON questions(order_num)",
                
                # Индексы для таблицы answer_options
                "CREATE INDEX IF NOT EXISTS idx_answer_options_question_id ON answer_options(question_id)",
                "CREATE INDEX IF NOT EXISTS idx_answer_options_is_correct ON answer_options(is_correct)",
                
                # Составные индексы для связующих таблиц many-to-many
                "CREATE INDEX IF NOT EXISTS idx_course_subjects_course_id ON course_subjects(course_id)",
                "CREATE INDEX IF NOT EXISTS idx_course_subjects_subject_id ON course_subjects(subject_id)",
                "CREATE INDEX IF NOT EXISTS idx_course_subjects_both ON course_subjects(course_id, subject_id)",
            ]
            
            created_count = 0
            for index_sql in indexes_to_create:
                try:
                    await session.execute(text(index_sql))
                    index_name = index_sql.split("idx_")[1].split(" ")[0] if "idx_" in index_sql else "unknown"
                    print(f"  ✅ Создан индекс: idx_{index_name}")
                    created_count += 1
                except Exception as e:
                    if "already exists" in str(e).lower():
                        index_name = index_sql.split("idx_")[1].split(" ")[0] if "idx_" in index_sql else "unknown"
                        print(f"  ℹ️ Индекс уже существует: idx_{index_name}")
                    else:
                        print(f"  ❌ Ошибка создания индекса: {e}")
            
            await session.commit()
            print(f"📊 Создано новых индексов: {created_count}")
            
        except Exception as e:
            print(f"❌ Ошибка при создании индексов: {e}")
            await session.rollback()
            raise


async def optimize_postgresql_settings():
    """Оптимизация настроек PostgreSQL для лучшей производительности"""
    print("⚙️ Применение оптимизаций PostgreSQL...")
    
    async with get_db_session() as session:
        try:
            # Настройки для оптимизации производительности
            optimizations = [
                # Увеличиваем work_mem для сложных запросов
                "SET work_mem = '16MB'",
                
                # Оптимизация для небольших транзакций
                "SET synchronous_commit = 'off'",
                
                # Увеличиваем shared_buffers (только для текущей сессии)
                "SET effective_cache_size = '256MB'",
                
                # Оптимизация планировщика запросов
                "SET random_page_cost = 1.1",
                "SET seq_page_cost = 1.0",
                
                # Включаем автовакуум для поддержания производительности
                "SET autovacuum = 'on'",
            ]
            
            for optimization in optimizations:
                try:
                    await session.execute(text(optimization))
                    setting_name = optimization.split("SET ")[1].split(" =")[0]
                    print(f"  ✅ Применена настройка: {setting_name}")
                except Exception as e:
                    print(f"  ⚠️ Не удалось применить настройку: {optimization} - {e}")
            
            await session.commit()
            print("⚙️ Оптимизации PostgreSQL применены")
            
        except Exception as e:
            print(f"❌ Ошибка при применении оптимизаций: {e}")
            await session.rollback()


async def analyze_tables():
    """Анализ таблиц для обновления статистики планировщика"""
    print("📈 Анализ таблиц для обновления статистики...")
    
    async with get_db_session() as session:
        try:
            # Список основных таблиц для анализа
            tables_to_analyze = [
                "users", "students", "curators", "teachers", "managers",
                "courses", "subjects", "groups", "microtopics", "lessons",
                "homework", "questions", "answer_options", "course_subjects"
            ]
            
            for table in tables_to_analyze:
                try:
                    await session.execute(text(f"ANALYZE {table}"))
                    print(f"  ✅ Проанализирована таблица: {table}")
                except Exception as e:
                    print(f"  ⚠️ Ошибка анализа таблицы {table}: {e}")
            
            await session.commit()
            print("📈 Анализ таблиц завершен")
            
        except Exception as e:
            print(f"❌ Ошибка при анализе таблиц: {e}")
            await session.rollback()


async def get_database_stats():
    """Получение статистики базы данных"""
    print("📊 Получение статистики базы данных...")
    
    async with get_db_session() as session:
        try:
            # Размер базы данных
            result = await session.execute(text(
                "SELECT pg_size_pretty(pg_database_size(current_database())) as db_size"
            ))
            db_size = result.scalar()
            print(f"  📦 Размер базы данных: {db_size}")
            
            # Количество записей в основных таблицах
            tables = ["users", "students", "homework", "questions", "answer_options"]
            for table in tables:
                try:
                    result = await session.execute(text(f"SELECT COUNT(*) FROM {table}"))
                    count = result.scalar()
                    print(f"  📋 Записей в таблице {table}: {count}")
                except Exception as e:
                    print(f"  ⚠️ Ошибка подсчета записей в {table}: {e}")
            
            # Информация об индексах
            result = await session.execute(text("""
                SELECT schemaname, tablename, indexname, idx_tup_read, idx_tup_fetch
                FROM pg_stat_user_indexes 
                WHERE idx_tup_read > 0 
                ORDER BY idx_tup_read DESC 
                LIMIT 10
            """))
            indexes = result.fetchall()
            
            if indexes:
                print("  🔍 Топ-10 используемых индексов:")
                for idx in indexes:
                    print(f"    - {idx.indexname}: {idx.idx_tup_read} чтений")
            
        except Exception as e:
            print(f"❌ Ошибка получения статистики: {e}")


async def main():
    """Основная функция оптимизации"""
    print("🚀 Запуск оптимизации базы данных...")
    
    try:
        # Создание индексов
        await create_performance_indexes()
        
        # Применение оптимизаций PostgreSQL
        await optimize_postgresql_settings()
        
        # Анализ таблиц
        await analyze_tables()
        
        # Получение статистики
        await get_database_stats()
        
        print("🎉 Оптимизация базы данных завершена успешно!")
        print("💡 Рекомендации:")
        print("   - Запускайте этот скрипт периодически для поддержания производительности")
        print("   - Мониторьте использование индексов через pg_stat_user_indexes")
        print("   - Настройте автовакуум для автоматической очистки")
        
    except Exception as e:
        print(f"❌ Ошибка при оптимизации базы данных: {e}")
        sys.exit(1)


if __name__ == "__main__":
    asyncio.run(main())
